import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { UpdateAssociationStatusResultFilterSensitiveLog, } from "../models/models_2";
import { de_UpdateAssociationStatusCommand, se_UpdateAssociationStatusCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class UpdateAssociationStatusCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "UpdateAssociationStatus", {})
    .n("SSMClient", "UpdateAssociationStatusCommand")
    .f(void 0, UpdateAssociationStatusResultFilterSensitiveLog)
    .ser(se_UpdateAssociationStatusCommand)
    .de(de_UpdateAssociationStatusCommand)
    .build() {
}
