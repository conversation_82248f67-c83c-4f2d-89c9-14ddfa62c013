import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_ListDocumentsCommand, se_ListDocumentsCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class ListDocumentsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "ListDocuments", {})
    .n("SSMClient", "ListDocumentsCommand")
    .f(void 0, void 0)
    .ser(se_ListDocumentsCommand)
    .de(de_ListDocumentsCommand)
    .build() {
}
