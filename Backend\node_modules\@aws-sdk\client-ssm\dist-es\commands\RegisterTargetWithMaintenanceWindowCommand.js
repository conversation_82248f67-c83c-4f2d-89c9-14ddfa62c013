import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { RegisterTargetWithMaintenanceWindowRequestFilterSensitiveLog, } from "../models/models_2";
import { de_RegisterTargetWithMaintenanceWindowCommand, se_RegisterTargetWithMaintenanceWindowCommand, } from "../protocols/Aws_json1_1";
export { $Command };
export class RegisterTargetWithMaintenanceWindowCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "RegisterTargetWithMaintenanceWindow", {})
    .n("SSMClient", "RegisterTargetWithMaintenanceWindowCommand")
    .f(RegisterTargetWithMaintenanceWindowRequestFilterSensitiveLog, void 0)
    .ser(se_RegisterTargetWithMaintenanceWindowCommand)
    .de(de_RegisterTargetWithMaintenanceWindowCommand)
    .build() {
}
