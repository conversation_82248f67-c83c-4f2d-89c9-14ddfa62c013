import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListCommandsResultFilterSensitiveLog } from "../models/models_1";
import { de_ListCommandsCommand, se_ListCommandsCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class ListCommandsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "ListCommands", {})
    .n("SSMClient", "ListCommandsCommand")
    .f(void 0, ListCommandsResultFilterSensitiveLog)
    .ser(se_ListCommandsCommand)
    .de(de_ListCommandsCommand)
    .build() {
}
