import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  AddTagsToResourceRequest,
  AddTagsToResourceResult,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface AddTagsToResourceCommandInput
  extends AddTagsToResourceRequest {}
export interface AddTagsToResourceCommandOutput
  extends AddTagsToResourceResult,
    __MetadataBearer {}
declare const AddTagsToResourceCommand_base: {
  new (
    input: AddTagsToResourceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AddTagsToResourceCommandInput,
    AddTagsToResourceCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: AddTagsToResourceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AddTagsToResourceCommandInput,
    AddTagsToResourceCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AddTagsToResourceCommand extends AddTagsToResourceCommand_base {
  protected static __types: {
    api: {
      input: AddTagsToResourceRequest;
      output: {};
    };
    sdk: {
      input: AddTagsToResourceCommandInput;
      output: AddTagsToResourceCommandOutput;
    };
  };
}
