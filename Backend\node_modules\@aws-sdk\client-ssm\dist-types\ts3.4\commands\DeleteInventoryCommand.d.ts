import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteInventoryRequest,
  DeleteInventoryResult,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteInventoryCommandInput extends DeleteInventoryRequest {}
export interface DeleteInventoryCommandOutput
  extends DeleteInventoryResult,
    __MetadataBearer {}
declare const DeleteInventoryCommand_base: {
  new (
    input: DeleteInventoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteInventoryCommandInput,
    DeleteInventoryCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteInventoryCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteInventoryCommandInput,
    DeleteInventoryCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteInventoryCommand extends DeleteInventoryCommand_base {
  protected static __types: {
    api: {
      input: DeleteInventoryRequest;
      output: DeleteInventoryResult;
    };
    sdk: {
      input: DeleteInventoryCommandInput;
      output: DeleteInventoryCommandOutput;
    };
  };
}
