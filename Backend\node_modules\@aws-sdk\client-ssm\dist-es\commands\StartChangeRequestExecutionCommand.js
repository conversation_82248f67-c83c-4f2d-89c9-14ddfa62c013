import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_StartChangeRequestExecutionCommand, se_StartChangeRequestExecutionCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class StartChangeRequestExecutionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "StartChangeRequestExecution", {})
    .n("SSMClient", "StartChangeRequestExecutionCommand")
    .f(void 0, void 0)
    .ser(se_StartChangeRequestExecutionCommand)
    .de(de_StartChangeRequestExecutionCommand)
    .build() {
}
