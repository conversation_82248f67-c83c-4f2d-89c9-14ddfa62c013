import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteDocumentRequest,
  DeleteDocumentResult,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteDocumentCommandInput extends DeleteDocumentRequest {}
export interface DeleteDocumentCommandOutput
  extends DeleteDocumentResult,
    __MetadataBearer {}
declare const DeleteDocumentCommand_base: {
  new (
    input: DeleteDocumentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDocumentCommandInput,
    DeleteDocumentCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteDocumentCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteDocumentCommandInput,
    DeleteDocumentCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteDocumentCommand extends DeleteDocumentCommand_base {
  protected static __types: {
    api: {
      input: DeleteDocumentRequest;
      output: {};
    };
    sdk: {
      input: DeleteDocumentCommandInput;
      output: DeleteDocumentCommandOutput;
    };
  };
}
