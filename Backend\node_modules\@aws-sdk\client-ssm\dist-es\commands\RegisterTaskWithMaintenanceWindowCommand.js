import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { RegisterTaskWithMaintenanceWindowRequestFilterSensitiveLog, } from "../models/models_2";
import { de_RegisterTaskWithMaintenanceWindowCommand, se_RegisterTaskWithMaintenanceWindowCommand, } from "../protocols/Aws_json1_1";
export { $Command };
export class RegisterTaskWithMaintenanceWindowCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "RegisterTaskWithMaintenanceWindow", {})
    .n("SSMClient", "RegisterTaskWithMaintenanceWindowCommand")
    .f(RegisterTaskWithMaintenanceWindowRequestFilterSensitiveLog, void 0)
    .ser(se_RegisterTaskWithMaintenanceWindowCommand)
    .de(de_RegisterTaskWithMaintenanceWindowCommand)
    .build() {
}
