import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { ListAssociationVersionsResultFilterSensitiveLog, } from "../models/models_1";
import { de_ListAssociationVersionsCommand, se_ListAssociationVersionsCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class ListAssociationVersionsCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "ListAssociationVersions", {})
    .n("SSMClient", "ListAssociationVersionsCommand")
    .f(void 0, ListAssociationVersionsResultFilterSensitiveLog)
    .ser(se_ListAssociationVersionsCommand)
    .de(de_ListAssociationVersionsCommand)
    .build() {
}
