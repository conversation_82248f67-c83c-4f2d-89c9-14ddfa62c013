import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { SendCommandRequestFilterSensitiveLog, SendCommandResultFilterSensitiveLog, } from "../models/models_2";
import { de_SendCommandCommand, se_SendCommandCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class SendCommandCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "SendCommand", {})
    .n("SSMClient", "SendCommandCommand")
    .f(SendCommandRequestFilterSensitiveLog, SendCommandResultFilterSensitiveLog)
    .ser(se_SendCommandCommand)
    .de(de_SendCommandCommand)
    .build() {
}
