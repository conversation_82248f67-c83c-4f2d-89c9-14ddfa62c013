import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { PutParameterRequestFilterSensitiveLog } from "../models/models_1";
import { de_PutParameterCommand, se_PutParameterCommand } from "../protocols/Aws_json1_1";
export { $Command };
export class PutParameterCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonSSM", "PutParameter", {})
    .n("SSMClient", "PutParameterCommand")
    .f(PutParameterRequestFilterSensitiveLog, void 0)
    .ser(se_PutParameterCommand)
    .de(de_PutParameterCommand)
    .build() {
}
