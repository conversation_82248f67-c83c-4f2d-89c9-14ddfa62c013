import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DeleteMaintenanceWindowRequest,
  DeleteMaintenanceWindowResult,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface DeleteMaintenanceWindowCommandInput
  extends DeleteMaintenanceWindowRequest {}
export interface DeleteMaintenanceWindowCommandOutput
  extends DeleteMaintenanceWindowResult,
    __MetadataBearer {}
declare const DeleteMaintenanceWindowCommand_base: {
  new (
    input: DeleteMaintenanceWindowCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteMaintenanceWindowCommandInput,
    DeleteMaintenanceWindowCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteMaintenanceWindowCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteMaintenanceWindowCommandInput,
    DeleteMaintenanceWindowCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteMaintenanceWindowCommand extends DeleteMaintenanceWindowCommand_base {
  protected static __types: {
    api: {
      input: DeleteMaintenanceWindowRequest;
      output: DeleteMaintenanceWindowResult;
    };
    sdk: {
      input: DeleteMaintenanceWindowCommandInput;
      output: DeleteMaintenanceWindowCommandOutput;
    };
  };
}
